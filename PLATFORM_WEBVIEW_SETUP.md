# Platform-Specific WebView Setup

This project uses different versions of `react-native-webview` for iOS and Android platforms:

- **Android**: `react-native-webview` version `13.14.2`
- **iOS**: `react-native-webview` version `11.17.2` (installed as `react-native-webview-ios`)

## Setup

### 1. Dependencies Installed

The following packages are installed in `package.json`:

```json
{
  "react-native-webview": "13.14.2",
  "react-native-webview-ios": "npm:react-native-webview@11.17.2"
}
```

### 2. Platform-Specific Configuration

The `react-native.config.js` file is configured to use the correct version per platform:

```javascript
module.exports = {
  dependencies: {
    'react-native-webview': {
      platforms: {
        ios: null, // Disable for iOS, we'll use react-native-webview-ios
      },
    },
    'react-native-webview-ios': {
      platforms: {
        android: null, // Disable for Android
      },
    },
  },
  // ... other config
};
```

### 3. Platform WebView Component

A wrapper component `app/components/PlatformWebView.tsx` automatically selects the correct WebView version based on the platform:

```typescript
import { Platform } from 'react-native';

let WebView: any;

if (Platform.OS === 'ios') {
  // Use version 11.17.2 for iOS
  WebView = require('react-native-webview-ios').WebView;
} else {
  // Use version 13.14.2 for Android
  WebView = require('react-native-webview').WebView;
}

export { WebView };
export default WebView;
```

## Usage

### In Your Components

Instead of importing WebView directly from `react-native-webview`, import it from the platform wrapper:

```typescript
// ❌ Don't do this
import { WebView } from 'react-native-webview';

// ✅ Do this instead
import { WebView } from '@app/components/PlatformWebView';
```

### Example Usage

```typescript
import React from 'react';
import { View } from 'react-native';
import { WebView } from '@app/components/PlatformWebView';

const MyComponent = () => {
  return (
    <View style={{ flex: 1 }}>
      <WebView
        source={{ uri: 'https://example.com' }}
        startInLoadingState={true}
        originWhitelist={['https://*']}
      />
    </View>
  );
};
```

## Installation Steps (Already Completed)

If you need to set this up in a new project, follow these steps:

1. Install both versions:
   ```bash
   yarn add react-native-webview@13.14.2
   yarn add react-native-webview-ios@npm:react-native-webview@11.17.2
   ```

2. Configure autolinking in `react-native.config.js`

3. Create the platform wrapper component

4. Update existing WebView imports to use the wrapper

5. Clean and rebuild:
   ```bash
   # iOS
   cd ios && pod install && cd ..
   yarn ios

   # Android
   yarn android
   ```

## Verification

To verify the setup is working:

1. Check that iOS uses version 11.17.2:
   - Build for iOS and check the Podfile.lock for the WebView version
   
2. Check that Android uses version 13.14.2:
   - Build for Android and verify in the build logs

## Troubleshooting

If you encounter issues:

1. **Clean and reinstall**:
   ```bash
   yarn install
   cd ios && pod install && cd ..
   ```

2. **Reset Metro cache**:
   ```bash
   yarn start --reset-cache
   ```

3. **Clean builds**:
   ```bash
   # iOS
   cd ios && xcodebuild clean && cd ..
   
   # Android
   cd android && ./gradlew clean && cd ..
   ```

## Notes

- The platform wrapper ensures that the correct WebView version is used automatically
- All existing WebView props and methods work the same way
- TypeScript support is maintained through the wrapper component
- This setup allows for platform-specific bug fixes and feature requirements
