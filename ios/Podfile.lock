PODS:
  - abseil/algorithm (1.20240722.0):
    - abseil/algorithm/algorithm (= 1.20240722.0)
    - abseil/algorithm/container (= 1.20240722.0)
  - abseil/algorithm/algorithm (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/algorithm/container (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base (1.20240722.0):
    - abseil/base/atomic_hook (= 1.20240722.0)
    - abseil/base/base (= 1.20240722.0)
    - abseil/base/base_internal (= 1.20240722.0)
    - abseil/base/config (= 1.20240722.0)
    - abseil/base/core_headers (= 1.20240722.0)
    - abseil/base/cycleclock_internal (= 1.20240722.0)
    - abseil/base/dynamic_annotations (= 1.20240722.0)
    - abseil/base/endian (= 1.20240722.0)
    - abseil/base/errno_saver (= 1.20240722.0)
    - abseil/base/fast_type_id (= 1.20240722.0)
    - abseil/base/log_severity (= 1.20240722.0)
    - abseil/base/malloc_internal (= 1.20240722.0)
    - abseil/base/no_destructor (= 1.20240722.0)
    - abseil/base/nullability (= 1.20240722.0)
    - abseil/base/poison (= 1.20240722.0)
    - abseil/base/prefetch (= 1.20240722.0)
    - abseil/base/pretty_function (= 1.20240722.0)
    - abseil/base/raw_logging_internal (= 1.20240722.0)
    - abseil/base/spinlock_wait (= 1.20240722.0)
    - abseil/base/strerror (= 1.20240722.0)
    - abseil/base/throw_delegate (= 1.20240722.0)
  - abseil/base/atomic_hook (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/base (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/base_internal (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/config (1.20240722.0):
    - abseil/xcprivacy
  - abseil/base/core_headers (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/cycleclock_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/dynamic_annotations (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/endian (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/errno_saver (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/fast_type_id (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/log_severity (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/malloc_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/base/no_destructor (1.20240722.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/nullability (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/poison (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/xcprivacy
  - abseil/base/prefetch (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/pretty_function (1.20240722.0):
    - abseil/xcprivacy
  - abseil/base/raw_logging_internal (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/xcprivacy
  - abseil/base/spinlock_wait (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/strerror (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/throw_delegate (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/common (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/container/common_policy_traits (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/compressed_tuple (1.20240722.0):
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/container_memory (1.20240722.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/fixed_array (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_map (1.20240722.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_container_defaults
    - abseil/container/raw_hash_map
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/flat_hash_set (1.20240722.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_container_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hash_container_defaults (1.20240722.0):
    - abseil/base/config
    - abseil/container/hash_function_defaults
    - abseil/xcprivacy
  - abseil/container/hash_function_defaults (1.20240722.0):
    - abseil/base/config
    - abseil/container/common
    - abseil/hash/hash
    - abseil/meta/type_traits
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/container/hash_policy_traits (1.20240722.0):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hashtable_debug_hooks (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/container/hashtablez_sampler (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/inlined_vector (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/inlined_vector_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/container/layout (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/raw_hash_map (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
    - abseil/xcprivacy
  - abseil/container/raw_hash_set (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/crc/cpu_detect (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/crc32c (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_cord_state (1.20240722.0):
    - abseil/base/config
    - abseil/base/no_destructor
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/crc_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/non_temporal_arm_intrinsics (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/non_temporal_memcpy (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
    - abseil/xcprivacy
  - abseil/debugging/bounded_utf8_length_sequence (1.20240722.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/debugging/debugging_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/debugging/decode_rust_punycode (1.20240722.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/debugging/bounded_utf8_length_sequence
    - abseil/debugging/utf8_for_code_point
    - abseil/xcprivacy
  - abseil/debugging/demangle_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/debugging/demangle_rust
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/debugging/demangle_rust (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/decode_rust_punycode
    - abseil/xcprivacy
  - abseil/debugging/examine_stack (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/xcprivacy
  - abseil/debugging/stacktrace (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/xcprivacy
  - abseil/debugging/symbolize (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/debugging/utf8_for_code_point (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/flags/commandlineflag (1.20240722.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/commandlineflag_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/xcprivacy
  - abseil/flags/config (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/flag (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/commandlineflag
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/flag_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/flags/marshalling (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/path_util (1.20240722.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/private_handle_accessor (1.20240722.0):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/program_name (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/reflection (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/functional/any_invocable (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/bind_front (1.20240722.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/function_ref (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/hash/city (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/xcprivacy
  - abseil/hash/hash (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/hash/low_level_hash (1.20240722.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/log/absl_check (1.20240722.0):
    - abseil/log/internal/check_impl
    - abseil/xcprivacy
  - abseil/log/absl_log (1.20240722.0):
    - abseil/log/internal/log_impl
    - abseil/xcprivacy
  - abseil/log/absl_vlog_is_on (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/check (1.20240722.0):
    - abseil/log/internal/check_impl
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/globals (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/hash/hash
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/append_truncated (1.20240722.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/check_impl (1.20240722.0):
    - abseil/base/core_headers
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/check_op (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/nullguard
    - abseil/log/internal/nullstream
    - abseil/log/internal/strip
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/conditions (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/voidify
    - abseil/xcprivacy
  - abseil/log/internal/config (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/fnmatch (1.20240722.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/format (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/append_truncated
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/globals (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/strings/strings
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/log/internal/log_impl (1.20240722.0):
    - abseil/log/absl_vlog_is_on
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/log_message (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/examine_stack
    - abseil/log/globals
    - abseil/log/internal/append_truncated
    - abseil/log/internal/format
    - abseil/log/internal/globals
    - abseil/log/internal/log_sink_set
    - abseil/log/internal/nullguard
    - abseil/log/internal/proto
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/log/log_sink_registry
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/log_sink_set (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/cleanup/cleanup
    - abseil/log/globals
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/nullguard (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/nullstream (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/proto (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/strip (1.20240722.0):
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/log_message
    - abseil/log/internal/nullstream
    - abseil/xcprivacy
  - abseil/log/internal/vlog_config (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/log/internal/fnmatch
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/log/internal/voidify (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/log/log (1.20240722.0):
    - abseil/log/internal/log_impl
    - abseil/log/vlog_is_on
    - abseil/xcprivacy
  - abseil/log/log_entry (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/config
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/log_sink (1.20240722.0):
    - abseil/base/config
    - abseil/log/log_entry
    - abseil/xcprivacy
  - abseil/log/log_sink_registry (1.20240722.0):
    - abseil/base/config
    - abseil/log/internal/log_sink_set
    - abseil/log/log_sink
    - abseil/xcprivacy
  - abseil/log/vlog_is_on (1.20240722.0):
    - abseil/log/absl_vlog_is_on
    - abseil/xcprivacy
  - abseil/memory (1.20240722.0):
    - abseil/memory/memory (= 1.20240722.0)
  - abseil/memory/memory (1.20240722.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/meta (1.20240722.0):
    - abseil/meta/type_traits (= 1.20240722.0)
  - abseil/meta/type_traits (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/bits (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/int128 (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
    - abseil/types/compare
    - abseil/xcprivacy
  - abseil/numeric/representation (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/profiling/exponential_biased (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/profiling/sample_recorder (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/random/bit_gen_ref (1.20240722.0):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
    - abseil/xcprivacy
  - abseil/random/distributions (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/random/internal/distribution_caller (1.20240722.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/random/internal/fast_uniform_bits (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/fastmath (1.20240722.0):
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/random/internal/generate_real (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/iostream_state_saver (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/nonsecure_base (1.20240722.0):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/pcg_engine (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
    - abseil/xcprivacy
  - abseil/random/internal/platform (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/internal/pool_urbg (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/randen (1.20240722.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
    - abseil/xcprivacy
  - abseil/random/internal/randen_engine (1.20240722.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes (1.20240722.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes_impl (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/randen_slow (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/salted_seed_seq (1.20240722.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/seed_material (1.20240722.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/traits (1.20240722.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/uniform_helper (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/wide_multiply (1.20240722.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/random (1.20240722.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
    - abseil/xcprivacy
  - abseil/random/seed_gen_exception (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/seed_sequences (1.20240722.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/status (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/statusor (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/charset (1.20240722.0):
    - abseil/base/core_headers
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/strings/cord (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/compare
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cord_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_functions (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
    - abseil/xcprivacy
  - abseil/strings/cordz_handle (1.20240722.0):
    - abseil/base/config
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/strings/cordz_info (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_statistics (1.20240722.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_scope (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_tracker (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/has_ostream_operator (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/strings/str_format (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/str_format_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/string_view (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/xcprivacy
  - abseil/strings/strings (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/synchronization/graphcycles_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/synchronization/kernel_timeout_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/synchronization/synchronization (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/time (1.20240722.0):
    - abseil/time/internal (= 1.20240722.0)
    - abseil/time/time (= 1.20240722.0)
  - abseil/time/internal (1.20240722.0):
    - abseil/time/internal/cctz (= 1.20240722.0)
  - abseil/time/internal/cctz (1.20240722.0):
    - abseil/time/internal/cctz/civil_time (= 1.20240722.0)
    - abseil/time/internal/cctz/time_zone (= 1.20240722.0)
  - abseil/time/internal/cctz/civil_time (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/time/internal/cctz/time_zone (1.20240722.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
    - abseil/xcprivacy
  - abseil/time/time (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/types (1.20240722.0):
    - abseil/types/any (= 1.20240722.0)
    - abseil/types/bad_any_cast (= 1.20240722.0)
    - abseil/types/bad_any_cast_impl (= 1.20240722.0)
    - abseil/types/bad_optional_access (= 1.20240722.0)
    - abseil/types/bad_variant_access (= 1.20240722.0)
    - abseil/types/compare (= 1.20240722.0)
    - abseil/types/optional (= 1.20240722.0)
    - abseil/types/span (= 1.20240722.0)
    - abseil/types/variant (= 1.20240722.0)
  - abseil/types/any (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/bad_any_cast (1.20240722.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
    - abseil/xcprivacy
  - abseil/types/bad_any_cast_impl (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_optional_access (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_variant_access (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/compare (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/optional (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/span (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/variant (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/utility/utility (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/xcprivacy (1.20240722.0)
  - amplitude-react-native (1.5.0):
    - React-Core
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - boost (1.84.0)
  - BoringSSL-GRPC (0.0.37):
    - BoringSSL-GRPC/Implementation (= 0.0.37)
    - BoringSSL-GRPC/Interface (= 0.0.37)
  - BoringSSL-GRPC/Implementation (0.0.37):
    - BoringSSL-GRPC/Interface (= 0.0.37)
  - BoringSSL-GRPC/Interface (0.0.37)
  - Clarity (3.0.9)
  - DoubleConversion (1.1.6)
  - fast_float (6.1.4)
  - FBAEMKit (17.0.3):
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBAudienceNetwork (6.11.2)
  - FBLazyVector (0.79.2)
  - FBSDKCoreKit (17.0.3):
    - FBAEMKit (= 17.0.3)
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBSDKCoreKit_Basics (17.0.3)
  - FBSDKGamingServicesKit (17.0.3):
    - FBSDKCoreKit (= 17.0.3)
    - FBSDKCoreKit_Basics (= 17.0.3)
    - FBSDKShareKit (= 17.0.3)
  - FBSDKLoginKit (17.0.3):
    - FBSDKCoreKit (= 17.0.3)
  - FBSDKShareKit (17.0.3):
    - FBSDKCoreKit (= 17.0.3)
  - Firebase/Auth (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.15.0)
  - Firebase/Core (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.15.0)
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Firestore (11.15.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.15.0)
  - Firebase/Messaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.15.0)
  - FirebaseAnalytics (11.15.0):
    - FirebaseAnalytics/Default (= 11.15.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/Default (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/Default (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.15.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseFirestore (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - FirebaseFirestoreInternal (= 11.15.0)
    - FirebaseSharedSwift (~> 11.0)
  - FirebaseFirestoreInternal (11.15.0):
    - abseil/algorithm (~> 1.20240722.0)
    - abseil/base (~> 1.20240722.0)
    - abseil/container/flat_hash_map (~> 1.20240722.0)
    - abseil/memory (~> 1.20240722.0)
    - abseil/meta (~> 1.20240722.0)
    - abseil/strings/strings (~> 1.20240722.0)
    - abseil/time (~> 1.20240722.0)
    - abseil/types (~> 1.20240722.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - "gRPC-C++ (~> 1.69.0)"
    - gRPC-Core (~> 1.69.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseSharedSwift (11.15.0)
  - fmt (11.0.2)
  - glog (0.3.5)
  - GoogleAdsOnDeviceConversion (2.1.0):
    - GoogleUtilities/Logger (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Core (11.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Default (11.15.0):
    - GoogleAdsOnDeviceConversion (= 2.1.0)
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleAppMeasurement/IdentitySupport (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/IdentitySupport (11.15.0):
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities (8.1.0):
    - GoogleUtilities/AppDelegateSwizzler (= 8.1.0)
    - GoogleUtilities/Environment (= 8.1.0)
    - GoogleUtilities/Logger (= 8.1.0)
    - GoogleUtilities/MethodSwizzler (= 8.1.0)
    - GoogleUtilities/Network (= 8.1.0)
    - "GoogleUtilities/NSData+zlib (= 8.1.0)"
    - GoogleUtilities/Privacy (= 8.1.0)
    - GoogleUtilities/Reachability (= 8.1.0)
    - GoogleUtilities/SwizzlerTestHelpers (= 8.1.0)
    - GoogleUtilities/UserDefaults (= 8.1.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (8.1.0):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.69.0)":
    - "gRPC-C++/Implementation (= 1.69.0)"
    - "gRPC-C++/Interface (= 1.69.0)"
  - "gRPC-C++/Implementation (1.69.0)":
    - abseil/algorithm/container (~> 1.20240722.0)
    - abseil/base/base (~> 1.20240722.0)
    - abseil/base/config (~> 1.20240722.0)
    - abseil/base/core_headers (~> 1.20240722.0)
    - abseil/base/log_severity (~> 1.20240722.0)
    - abseil/base/no_destructor (~> 1.20240722.0)
    - abseil/cleanup/cleanup (~> 1.20240722.0)
    - abseil/container/flat_hash_map (~> 1.20240722.0)
    - abseil/container/flat_hash_set (~> 1.20240722.0)
    - abseil/container/inlined_vector (~> 1.20240722.0)
    - abseil/flags/flag (~> 1.20240722.0)
    - abseil/flags/marshalling (~> 1.20240722.0)
    - abseil/functional/any_invocable (~> 1.20240722.0)
    - abseil/functional/bind_front (~> 1.20240722.0)
    - abseil/functional/function_ref (~> 1.20240722.0)
    - abseil/hash/hash (~> 1.20240722.0)
    - abseil/log/absl_check (~> 1.20240722.0)
    - abseil/log/absl_log (~> 1.20240722.0)
    - abseil/log/check (~> 1.20240722.0)
    - abseil/log/globals (~> 1.20240722.0)
    - abseil/log/log (~> 1.20240722.0)
    - abseil/memory/memory (~> 1.20240722.0)
    - abseil/meta/type_traits (~> 1.20240722.0)
    - abseil/numeric/bits (~> 1.20240722.0)
    - abseil/random/bit_gen_ref (~> 1.20240722.0)
    - abseil/random/distributions (~> 1.20240722.0)
    - abseil/random/random (~> 1.20240722.0)
    - abseil/status/status (~> 1.20240722.0)
    - abseil/status/statusor (~> 1.20240722.0)
    - abseil/strings/cord (~> 1.20240722.0)
    - abseil/strings/str_format (~> 1.20240722.0)
    - abseil/strings/strings (~> 1.20240722.0)
    - abseil/synchronization/synchronization (~> 1.20240722.0)
    - abseil/time/time (~> 1.20240722.0)
    - abseil/types/optional (~> 1.20240722.0)
    - abseil/types/span (~> 1.20240722.0)
    - abseil/types/variant (~> 1.20240722.0)
    - abseil/utility/utility (~> 1.20240722.0)
    - "gRPC-C++/Interface (= 1.69.0)"
    - "gRPC-C++/Privacy (= 1.69.0)"
    - gRPC-Core (= 1.69.0)
  - "gRPC-C++/Interface (1.69.0)"
  - "gRPC-C++/Privacy (1.69.0)"
  - gRPC-Core (1.69.0):
    - gRPC-Core/Implementation (= 1.69.0)
    - gRPC-Core/Interface (= 1.69.0)
  - gRPC-Core/Implementation (1.69.0):
    - abseil/algorithm/container (~> 1.20240722.0)
    - abseil/base/base (~> 1.20240722.0)
    - abseil/base/config (~> 1.20240722.0)
    - abseil/base/core_headers (~> 1.20240722.0)
    - abseil/base/log_severity (~> 1.20240722.0)
    - abseil/base/no_destructor (~> 1.20240722.0)
    - abseil/cleanup/cleanup (~> 1.20240722.0)
    - abseil/container/flat_hash_map (~> 1.20240722.0)
    - abseil/container/flat_hash_set (~> 1.20240722.0)
    - abseil/container/inlined_vector (~> 1.20240722.0)
    - abseil/flags/flag (~> 1.20240722.0)
    - abseil/flags/marshalling (~> 1.20240722.0)
    - abseil/functional/any_invocable (~> 1.20240722.0)
    - abseil/functional/bind_front (~> 1.20240722.0)
    - abseil/functional/function_ref (~> 1.20240722.0)
    - abseil/hash/hash (~> 1.20240722.0)
    - abseil/log/check (~> 1.20240722.0)
    - abseil/log/globals (~> 1.20240722.0)
    - abseil/log/log (~> 1.20240722.0)
    - abseil/memory/memory (~> 1.20240722.0)
    - abseil/meta/type_traits (~> 1.20240722.0)
    - abseil/numeric/bits (~> 1.20240722.0)
    - abseil/random/bit_gen_ref (~> 1.20240722.0)
    - abseil/random/distributions (~> 1.20240722.0)
    - abseil/random/random (~> 1.20240722.0)
    - abseil/status/status (~> 1.20240722.0)
    - abseil/status/statusor (~> 1.20240722.0)
    - abseil/strings/cord (~> 1.20240722.0)
    - abseil/strings/str_format (~> 1.20240722.0)
    - abseil/strings/strings (~> 1.20240722.0)
    - abseil/synchronization/synchronization (~> 1.20240722.0)
    - abseil/time/time (~> 1.20240722.0)
    - abseil/types/optional (~> 1.20240722.0)
    - abseil/types/span (~> 1.20240722.0)
    - abseil/types/variant (~> 1.20240722.0)
    - abseil/utility/utility (~> 1.20240722.0)
    - BoringSSL-GRPC (= 0.0.37)
    - gRPC-Core/Interface (= 1.69.0)
    - gRPC-Core/Privacy (= 1.69.0)
  - gRPC-Core/Interface (1.69.0)
  - gRPC-Core/Privacy (1.69.0)
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - leveldb-library (1.22.6)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (4.5.0)
  - lottie-react-native (7.2.5):
    - DoubleConversion
    - glog
    - lottie-ios (= 4.5.0)
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - NWWebSocket (0.5.7)
  - Permission-Camera (3.10.1):
    - RNPermissions
  - PromisesObjC (2.4.0)
  - pusher-websocket-react-native (1.3.1):
    - PusherSwift (~> 10.1.5)
    - React
  - PusherSwift (10.1.6):
    - NWWebSocket (~> 0.5.7)
    - TweetNacl (~> 1.0.0)
  - RCT-Folly (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Default (= 2024.11.18.00)
  - RCT-Folly/Default (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
  - RCT-Folly/Fabric (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
  - RCTDeprecation (0.79.2)
  - RCTRequired (0.79.2)
  - RCTTypeSafety (0.79.2):
    - FBLazyVector (= 0.79.2)
    - RCTRequired (= 0.79.2)
    - React-Core (= 0.79.2)
  - React (0.79.2):
    - React-Core (= 0.79.2)
    - React-Core/DevSupport (= 0.79.2)
    - React-Core/RCTWebSocket (= 0.79.2)
    - React-RCTActionSheet (= 0.79.2)
    - React-RCTAnimation (= 0.79.2)
    - React-RCTBlob (= 0.79.2)
    - React-RCTImage (= 0.79.2)
    - React-RCTLinking (= 0.79.2)
    - React-RCTNetwork (= 0.79.2)
    - React-RCTSettings (= 0.79.2)
    - React-RCTText (= 0.79.2)
    - React-RCTVibration (= 0.79.2)
  - React-callinvoker (0.79.2)
  - React-Codegen (0.1.0)
  - React-Core (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.79.2)
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.79.2)
    - React-Core/RCTWebSocket (= 0.79.2)
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.79.2)
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety (= 0.79.2)
    - React-Core/CoreModulesHeaders (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.79.2)
    - ReactCommon
    - SocketRocket (= 0.7.1)
  - React-cxxreact (0.79.2):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-debug (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
    - React-runtimeexecutor (= 0.79.2)
    - React-timing (= 0.79.2)
  - React-debug (0.79.2)
  - React-defaultsnativemodule (0.79.2):
    - RCT-Folly
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-idlecallbacksnativemodule
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
  - React-domnativemodule (0.79.2):
    - RCT-Folly
    - React-Fabric
    - React-FabricComponents
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.79.2)
    - React-Fabric/attributedstring (= 0.79.2)
    - React-Fabric/componentregistry (= 0.79.2)
    - React-Fabric/componentregistrynative (= 0.79.2)
    - React-Fabric/components (= 0.79.2)
    - React-Fabric/consistency (= 0.79.2)
    - React-Fabric/core (= 0.79.2)
    - React-Fabric/dom (= 0.79.2)
    - React-Fabric/imagemanager (= 0.79.2)
    - React-Fabric/leakchecker (= 0.79.2)
    - React-Fabric/mounting (= 0.79.2)
    - React-Fabric/observers (= 0.79.2)
    - React-Fabric/scheduler (= 0.79.2)
    - React-Fabric/telemetry (= 0.79.2)
    - React-Fabric/templateprocessor (= 0.79.2)
    - React-Fabric/uimanager (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.79.2)
    - React-Fabric/components/root (= 0.79.2)
    - React-Fabric/components/scrollview (= 0.79.2)
    - React-Fabric/components/view (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/consistency (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/core (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.79.2)
    - React-FabricComponents/textlayoutmanager (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.79.2)
    - React-FabricComponents/components/iostextinput (= 0.79.2)
    - React-FabricComponents/components/modal (= 0.79.2)
    - React-FabricComponents/components/rncore (= 0.79.2)
    - React-FabricComponents/components/safeareaview (= 0.79.2)
    - React-FabricComponents/components/scrollview (= 0.79.2)
    - React-FabricComponents/components/text (= 0.79.2)
    - React-FabricComponents/components/textinput (= 0.79.2)
    - React-FabricComponents/components/unimplementedview (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired (= 0.79.2)
    - RCTTypeSafety (= 0.79.2)
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-jsiexecutor (= 0.79.2)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
  - React-featureflagsnativemodule (0.79.2):
    - RCT-Folly
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
  - React-graphics (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-idlecallbacksnativemodule (0.79.2):
    - glog
    - RCT-Folly
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-ImageManager (0.79.2):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jsc (0.79.2):
    - React-jsc/Fabric (= 0.79.2)
    - React-jsi (= 0.79.2)
  - React-jsc/Fabric (0.79.2):
    - React-jsi (= 0.79.2)
  - React-jserrorhandler (0.79.2):
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
  - React-jsi (0.79.2):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly (= 2024.11.18.00)
  - React-jsiexecutor (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-perflogger (= 0.79.2)
  - React-jsinspector (0.79.2):
    - DoubleConversion
    - glog
    - RCT-Folly
    - React-featureflags
    - React-jsi
    - React-jsinspectortracing
    - React-perflogger (= 0.79.2)
    - React-runtimeexecutor (= 0.79.2)
  - React-jsinspectortracing (0.79.2):
    - RCT-Folly
    - React-oscompat
  - React-jsitooling (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
  - React-jsitracing (0.79.2):
    - React-jsi
  - React-logger (0.79.2):
    - glog
  - React-Mapbuffer (0.79.2):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.79.2):
    - RCT-Folly
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
  - react-native-audio-session (0.0.6):
    - React-Core
  - react-native-cameraroll (7.10.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-clarity (4.3.1):
    - Clarity (= 3.0.9)
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - ReactCommon/turbomodule/core
  - react-native-compressor (1.12.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-config (1.5.5):
    - react-native-config/App (= 1.5.5)
  - react-native-config/App (1.5.5):
    - React-Core
  - react-native-config/Extension (1.5.5)
  - react-native-document-picker (10.1.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-fbsdk-next (13.0.0):
    - React-Core
    - react-native-fbsdk-next/Core (= 13.0.0)
    - react-native-fbsdk-next/Login (= 13.0.0)
    - react-native-fbsdk-next/Share (= 13.0.0)
  - react-native-fbsdk-next/Core (13.0.0):
    - FBSDKCoreKit (~> 17.0.0)
    - React-Core
  - react-native-fbsdk-next/Login (13.0.0):
    - FBSDKLoginKit (~> 17.0.0)
    - React-Core
  - react-native-fbsdk-next/Share (13.0.0):
    - FBSDKGamingServicesKit (~> 17.0.0)
    - FBSDKShareKit (~> 17.0.0)
    - React-Core
  - react-native-image-picker (7.2.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-month-year-picker (1.9.0):
    - React
  - react-native-pager-view (6.8.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-pinchable (0.2.1):
    - React-Core
  - react-native-safe-area-context (5.5.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - react-native-safe-area-context/common (= 5.5.2)
    - react-native-safe-area-context/fabric (= 5.5.2)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/common (5.5.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/fabric (5.5.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-sensitive-info (5.5.8):
    - React
  - react-native-skia (2.1.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React
    - React-callinvoker
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-slider (4.5.7):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - react-native-slider/common (= 4.5.7)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-slider/common (4.5.7):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-version-check (3.5.0):
    - React-Core
  - react-native-video (6.16.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - react-native-video/Video (= 6.16.1)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-video/Fabric (6.16.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-video/Video (6.16.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - react-native-video/Fabric
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-view-shot (4.0.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-webview (13.14.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-NativeModulesApple (0.79.2):
    - glog
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-oscompat (0.79.2)
  - React-perflogger (0.79.2):
    - DoubleConversion
    - RCT-Folly (= 2024.11.18.00)
  - React-performancetimeline (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
  - React-RCTActionSheet (0.79.2):
    - React-Core/RCTActionSheetHeaders (= 0.79.2)
  - React-RCTAnimation (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTAppDelegate (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimescheduler
    - React-utils
    - ReactCommon
  - React-RCTBlob (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - RCT-Folly (= 2024.11.18.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.79.2):
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-jsinspector
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTFBReactNativeSpec (0.79.2):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTImage (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.79.2):
    - React-Core/RCTLinkingHeaders (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.79.2)
  - React-RCTNetwork (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTRuntime (0.79.2):
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsinspector
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
  - React-RCTSettings (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTText (0.79.2):
    - React-Core/RCTTextHeaders (= 0.79.2)
    - Yoga
  - React-RCTVibration (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-rendererconsistency (0.79.2)
  - React-renderercss (0.79.2):
    - React-debug
    - React-utils
  - React-rendererdebug (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - RCT-Folly (= 2024.11.18.00)
    - React-debug
  - React-rncore (0.79.2)
  - React-RuntimeApple (0.79.2):
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jsc
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.79.2):
    - glog
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-jsc
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.79.2):
    - React-jsi (= 0.79.2)
  - React-runtimescheduler (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsc
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.79.2)
  - React-utils (0.79.2):
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-debug
    - React-jsc
    - React-jsi (= 0.79.2)
  - ReactAppDependencyProvider (0.79.2):
    - ReactCodegen
  - ReactCodegen (0.79.2):
    - DoubleConversion
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.79.2):
    - ReactCommon/turbomodule (= 0.79.2)
  - ReactCommon/turbomodule (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
    - ReactCommon/turbomodule/bridging (= 0.79.2)
    - ReactCommon/turbomodule/core (= 0.79.2)
  - ReactCommon/turbomodule/bridging (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
  - ReactCommon/turbomodule/core (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-cxxreact (= 0.79.2)
    - React-debug (= 0.79.2)
    - React-featureflags (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
    - React-utils (= 0.79.2)
  - ReactNativeAdsFacebook (7.1.1):
    - FBAudienceNetwork (= 6.11.2)
    - FBSDKCoreKit_Basics
    - FBSDKGamingServicesKit
    - React
  - ReactNativeExceptionHandler (2.10.10):
    - React-Core
  - RecaptchaInterop (101.0.0)
  - RNAppleAuthentication (2.4.1):
    - React-Core
  - RNBootSplash (5.5.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNCAsyncStorage (2.2.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNCClipboard (1.16.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNCPushNotificationIOS (1.11.0):
    - React-Core
  - RNDateTimePicker (8.4.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNDeviceInfo (10.14.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBApp (22.4.0):
    - Firebase/CoreOnly (= 11.15.0)
    - React-Core
  - RNFBAuth (22.4.0):
    - Firebase/Auth (= 11.15.0)
    - React-Core
    - RNFBApp
  - RNFBFirestore (22.4.0):
    - Firebase/Firestore (= 11.15.0)
    - React-Core
    - RNFBApp
  - RNFBMessaging (22.4.0):
    - Firebase/Messaging (= 11.15.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.27.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNGoogleSignin (15.0.0):
    - DoubleConversion
    - glog
    - GoogleSignIn (~> 8.0)
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNImageCropPicker (0.37.3):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.37.3)
    - TOCropViewController
  - RNImageCropPicker/QBImagePickerController (0.37.3):
    - React-Core
    - React-RCTImage
    - TOCropViewController
  - RNLocalize (3.5.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNPermissions (3.10.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated (3.19.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.19.0)
    - RNReanimated/worklets (= 3.19.0)
    - Yoga
  - RNReanimated/reanimated (3.19.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.19.0)
    - Yoga
  - RNReanimated/reanimated/apple (3.19.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.19.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/worklets/apple (= 3.19.0)
    - Yoga
  - RNReanimated/worklets/apple (3.19.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (4.13.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.13.1)
    - Yoga
  - RNScreens/common (4.13.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNShare (12.1.1):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSVG (15.12.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.12.0)
    - Yoga
  - RNSVG/common (15.12.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNVectorIcons (10.3.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.7.1)
  - Stripe (23.28.3):
    - StripeApplePay (= 23.28.3)
    - StripeCore (= 23.28.3)
    - StripePayments (= 23.28.3)
    - StripePaymentsUI (= 23.28.3)
    - StripeUICore (= 23.28.3)
  - stripe-react-native (0.38.6):
    - React-Core
    - Stripe (~> 23.28.0)
    - StripeApplePay (~> 23.28.0)
    - StripeFinancialConnections (~> 23.28.0)
    - StripePayments (~> 23.28.0)
    - StripePaymentSheet (~> 23.28.0)
    - StripePaymentsUI (~> 23.28.0)
  - StripeApplePay (23.28.3):
    - StripeCore (= 23.28.3)
  - StripeCore (23.28.3)
  - StripeFinancialConnections (23.28.3):
    - StripeCore (= 23.28.3)
    - StripeUICore (= 23.28.3)
  - StripePayments (23.28.3):
    - StripeCore (= 23.28.3)
    - StripePayments/Stripe3DS2 (= 23.28.3)
  - StripePayments/Stripe3DS2 (23.28.3):
    - StripeCore (= 23.28.3)
  - StripePaymentSheet (23.28.3):
    - StripeApplePay (= 23.28.3)
    - StripeCore (= 23.28.3)
    - StripePayments (= 23.28.3)
    - StripePaymentsUI (= 23.28.3)
  - StripePaymentsUI (23.28.3):
    - StripeCore (= 23.28.3)
    - StripePayments (= 23.28.3)
    - StripeUICore (= 23.28.3)
  - StripeUICore (23.28.3):
    - StripeCore (= 23.28.3)
  - TOCropViewController (2.7.4)
  - TweetNacl (1.0.2)
  - Yoga (0.0.0)

DEPENDENCIES:
  - "amplitude-react-native (from `../node_modules/@amplitude/analytics-react-native`)"
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBSDKCoreKit
  - FBSDKLoginKit
  - FBSDKShareKit
  - Firebase/Auth
  - Firebase/Core
  - Firebase/Firestore
  - FirebaseAppCheckInterop
  - FirebaseAuthInterop
  - FirebaseCore
  - FirebaseCoreExtension
  - FirebaseFirestoreInternal
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera/Permission-Camera.podspec`)
  - "pusher-websocket-react-native (from `../node_modules/@pusher/pusher-websocket-react-native`)"
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jsc/Fabric (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsinspectortracing (from `../node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)
  - React-jsitooling (from `../node_modules/react-native/ReactCommon/jsitooling`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-audio-session (from `../node_modules/react-native-audio-session`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-clarity (from `../node_modules/react-native-clarity`)
  - react-native-compressor (from `../node_modules/react-native-compressor`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-config/Extension (from `../node_modules/react-native-config`)
  - "react-native-document-picker (from `../node_modules/@react-native-documents/picker`)"
  - react-native-fbsdk-next (from `../node_modules/react-native-fbsdk-next`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-month-year-picker (from `../node_modules/react-native-month-year-picker`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-pinchable (from `../node_modules/react-native-pinchable`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-sensitive-info (from `../node_modules/react-native-sensitive-info`)
  - "react-native-skia (from `../node_modules/@shopify/react-native-skia`)"
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-version-check (from `../node_modules/react-native-version-check`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-oscompat (from `../node_modules/react-native/ReactCommon/oscompat`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTRuntime (from `../node_modules/react-native/React/Runtime`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-renderercss (from `../node_modules/react-native/ReactCommon/react/renderer/css`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeAdsFacebook (from `../node_modules/react-native-fbads`)
  - ReactNativeExceptionHandler (from `../node_modules/react-native-exception-handler`)
  - RecaptchaInterop
  - "RNAppleAuthentication (from `../node_modules/@invertase/react-native-apple-authentication`)"
  - RNBootSplash (from `../node_modules/react-native-bootsplash`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBAuth (from `../node_modules/@react-native-firebase/auth`)"
  - "RNFBFirestore (from `../node_modules/@react-native-firebase/firestore`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - "stripe-react-native (from `../node_modules/@stripe/stripe-react-native`)"
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - abseil
    - AppAuth
    - AppCheckCore
    - BoringSSL-GRPC
    - Clarity
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKGamingServicesKit
    - FBSDKLoginKit
    - FBSDKShareKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseSharedSwift
    - GoogleAdsOnDeviceConversion
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - libwebp
    - lottie-ios
    - nanopb
    - NWWebSocket
    - PromisesObjC
    - PusherSwift
    - React-Codegen
    - RecaptchaInterop
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore
    - TOCropViewController
    - TweetNacl

EXTERNAL SOURCES:
  amplitude-react-native:
    :path: "../node_modules/@amplitude/analytics-react-native"
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera/Permission-Camera.podspec"
  pusher-websocket-react-native:
    :path: "../node_modules/@pusher/pusher-websocket-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectortracing:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-audio-session:
    :path: "../node_modules/react-native-audio-session"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-clarity:
    :path: "../node_modules/react-native-clarity"
  react-native-compressor:
    :path: "../node_modules/react-native-compressor"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-document-picker:
    :path: "../node_modules/@react-native-documents/picker"
  react-native-fbsdk-next:
    :path: "../node_modules/react-native-fbsdk-next"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-month-year-picker:
    :path: "../node_modules/react-native-month-year-picker"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-pinchable:
    :path: "../node_modules/react-native-pinchable"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-sensitive-info:
    :path: "../node_modules/react-native-sensitive-info"
  react-native-skia:
    :path: "../node_modules/@shopify/react-native-skia"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-version-check:
    :path: "../node_modules/react-native-version-check"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeAdsFacebook:
    :path: "../node_modules/react-native-fbads"
  ReactNativeExceptionHandler:
    :path: "../node_modules/react-native-exception-handler"
  RNAppleAuthentication:
    :path: "../node_modules/@invertase/react-native-apple-authentication"
  RNBootSplash:
    :path: "../node_modules/react-native-bootsplash"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBAuth:
    :path: "../node_modules/@react-native-firebase/auth"
  RNFBFirestore:
    :path: "../node_modules/@react-native-firebase/firestore"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  stripe-react-native:
    :path: "../node_modules/@stripe/stripe-react-native"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  abseil: a05cc83bf02079535e17169a73c5be5ba47f714b
  amplitude-react-native: 8dc7c16ff85dc83cf35cec6d9a4380b205a3f45b
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  boost: 7e761d76ca2ce687f7cc98e698152abd03a18f90
  BoringSSL-GRPC: dded2a44897e45f28f08ae87a55ee4bcd19bc508
  Clarity: abf67b3421506aba1dedc2343f374917ab31aa3d
  DoubleConversion: cb417026b2400c8f53ae97020b2be961b59470cb
  fast_float: 06eeec4fe712a76acc9376682e4808b05ce978b6
  FBAEMKit: 9900b2edd99a2d21629a6277e6166f14c6215799
  FBAudienceNetwork: 4600bc7fa9bd925b1e397b7c9bbef02923062bbc
  FBLazyVector: 84b955f7b4da8b895faf5946f73748267347c975
  FBSDKCoreKit: 0791f8f68a8630931a4c12aa23a56cc021551596
  FBSDKCoreKit_Basics: 46d6b472c0dd0a5a7e972c025033d1c567f54eb4
  FBSDKGamingServicesKit: 599a63c235776b7c6288d5dfd7c02edab9a1d3b7
  FBSDKLoginKit: b4a4eba1d62eb452544411824f41689adabd5bd2
  FBSDKShareKit: eb1169eafe3c06f881dc6deed2d7979585b16959
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  FirebaseAnalytics: 6433dfd311ba78084fc93bdfc145e8cb75740eae
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: a6575e5fbf46b046c58dc211a28a5fbdd8d4c83b
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreExtension: edbd30474b5ccf04e5f001470bdf6ea616af2435
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseFirestore: 1e5fafdac2b2ef1ffc24034460b7b4821a15be96
  FirebaseFirestoreInternal: df9ab608a59a4e8eefd0796ed7652f3c1a88473a
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseMessaging: 3b26e2cee503815e01c3701236b020aa9b576f09
  FirebaseSharedSwift: e17c654ef1f1a616b0b33054e663ad1035c8fd40
  fmt: a40bb5bd0294ea969aaaba240a927bd33d878cdd
  glog: 5683914934d5b6e4240e497e0f4a3b42d1854183
  GoogleAdsOnDeviceConversion: 2be6297a4f048459e0ae17fad9bfd2844e10cf64
  GoogleAppMeasurement: 700dce7541804bec33db590a5c496b663fbe2539
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  "gRPC-C++": cc207623316fb041a7a3e774c252cf68a058b9e8
  gRPC-Core: 860978b7db482de8b4f5e10677216309b5ff6330
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: a881093fab623c467d3bce374367755c272bdd59
  lottie-react-native: 5a189514d03e731900c7ac027b67a22c9a4c3606
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  NWWebSocket: b4741420f1976e1dff4da3edad00c401e4f1d769
  Permission-Camera: 9b70902f34a83c10e198d2d01f0e453e58842776
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  pusher-websocket-react-native: e40c49a1e4ec96d4157375aebcf44943f0f8f62f
  PusherSwift: fa3d5f6587c20ad5790de87a5c9b150367b5f0f5
  RCT-Folly: 36fe2295e44b10d831836cc0d1daec5f8abcf809
  RCTDeprecation: 83ffb90c23ee5cea353bd32008a7bca100908f8c
  RCTRequired: eb7c0aba998009f47a540bec9e9d69a54f68136e
  RCTTypeSafety: 659ae318c09de0477fd27bbc9e140071c7ea5c93
  React: c2d3aa44c49bb34e4dfd49d3ee92da5ebacc1c1c
  React-callinvoker: 1bdfb7549b5af266d85757193b5069f60659ef9d
  React-Codegen: 4b8b4817cea7a54b83851d4c1f91f79aa73de30a
  React-Core: 178d02ff993b529ba023f6460ee0880fd055a416
  React-CoreModules: 15a85e6665d61678942da6ae485b351f4c699049
  React-cxxreact: 5e0114bdcd80b0364badf4536a4bc54a6f82f85e
  React-debug: a9861ea2196e886642887e29fd1d86c6eee93454
  React-defaultsnativemodule: 66e848ea30f4095d99b988cfc03ce0adddec72e9
  React-domnativemodule: eb63cc75ff6a6da1418b9f9574fc98d0c34dc377
  React-Fabric: 0ae2886e9e86705d88cc97896699a17f9bf7da49
  React-FabricComponents: 7087d7f7658404459630eb045292225ba7c5e513
  React-FabricImage: 48d75b1a105b787b97e8851e1f87a323b44878d4
  React-featureflags: 4ef61c283dfae8f327dbae70f41bb0399bd9e0fc
  React-featureflagsnativemodule: 7291578b39d7f0de8ac50beb1cb00bb550dc3d8d
  React-graphics: 57f903039ce34d3185b405dee07baf654038e06a
  React-idlecallbacksnativemodule: 9fc74de78a97d868e21a9749cfa2b94db243e990
  React-ImageManager: 21a10bb92dde7cf5ec29c139769630dadbac0cc2
  React-jsc: 6d02be64fbcb81865d0df66f5796b6e2a4213278
  React-jserrorhandler: 9e3d8f6b443400af96f3d0f9fadaecde5e4d185a
  React-jsi: 506113716b726967d649dd44d4adbd634a59c6bc
  React-jsiexecutor: e33ab4fc3e0bb9cb4372de873b625fe52e94a5f1
  React-jsinspector: d5aa0cfa11d35f102a9006e6ec7fd21e36294189
  React-jsinspectortracing: d9acc748525e22e4c7b7241ea39ecbd99bfa6c22
  React-jsitooling: 5bf7d347f145ae47bcecd4107ba7fe7539ebf55b
  React-jsitracing: 6e27cce907f23131472594523cd6e30737a754e5
  React-logger: 368570a253f00879a1e4fea24ed4047e72e7bbf3
  React-Mapbuffer: f581f01e9c766bbfb7d1f57a6f6ceb86bdc310f1
  React-microtasksnativemodule: 57d67ebb3504b3b28c2bc82727c9acc954522105
  react-native-audio-session: a92396d8eeae3e66eb29572d814e357acafe76d6
  react-native-cameraroll: 75cd4112886002599c739b51b0a818d25e0d50fb
  react-native-clarity: 0ad7b919383c48bb5867dccd7f3f4c830099b420
  react-native-compressor: ff4321c97b5ec61ab5b05c50e559b4d8686b5173
  react-native-config: 3367df9c1f25bb96197007ec531c7087ed4554c3
  react-native-document-picker: 657ca24db41d64a494b3387f02ebf3f48a42e461
  react-native-fbsdk-next: 84746ea062dc3b1a422154d7674ba4c5836b6d82
  react-native-image-picker: 5bfa7afb4e2973da49db5e95f1240ab41784891f
  react-native-month-year-picker: 948797e981ca502427024a34b17f545eb321bbff
  react-native-pager-view: 872c7cff56b32eedc0c436d02918cc54b19f5c4a
  react-native-pinchable: b4381b0807d15c12b14bfacd927128f52d96f0f1
  react-native-safe-area-context: 0f9a0fa4488367ae962e978344c5dfd983b39ec6
  react-native-sensitive-info: 400c6e3d27945e15c8cddb5b4b92233c85ee06f8
  react-native-skia: 1bd9168b420afe0a602dc9eb72d43bef4f7741ca
  react-native-slider: cf65582b93ee0d65fe2699b10bcf60eec1883556
  react-native-version-check: 54a471d70dadf0f72fc142e70f16e4bf473a91a5
  react-native-video: 75cc56e09bae001f9af02c80fa77e467862aeac8
  react-native-view-shot: 14d629e3c7e926f54f984bd18c28816657e6ecc1
  react-native-webview: 3bd78f7677ea3b4fa52c8db75705c79f58720530
  React-NativeModulesApple: 712d200fe5f9ad457fed3a566ad50ec21eee3db3
  React-oscompat: ef5df1c734f19b8003e149317d041b8ce1f7d29c
  React-perflogger: 6fd2f6811533e9c19a61e855c3033eecbf4ad2a0
  React-performancetimeline: df331d0764cc54204a73960408371111efbd34b7
  React-RCTActionSheet: a499b0d6d9793886b67ba3e16046a3fef2cdbbc3
  React-RCTAnimation: 2595dcb10a82216a511b54742f8c28d793852ac6
  React-RCTAppDelegate: 2276ae86b9c78e464768e1ec67e4a569a5775e15
  React-RCTBlob: c92600a7fd1b401c0602a9a3e6c515a95b14e8b1
  React-RCTFabric: 6868fa39a5586058ff98e7f7e23d51d8de1dfb8e
  React-RCTFBReactNativeSpec: 08eb43939d4753828940d016a15e8dbf1d2d7ca6
  React-RCTImage: dac5e9f8ec476aefe6e60ee640ebc1dfaf1a4dbe
  React-RCTLinking: 494b785a40d952a1dfbe712f43214376e5f0e408
  React-RCTNetwork: b3d7c30cd21793e268db107dd0980cb61b3c1c44
  React-RCTRuntime: 05e940e5767c0e2381d22d573efdb8d152653f2c
  React-RCTSettings: a060c7e381a3896104761b8eed7e284d95e37df3
  React-RCTText: 4f272b72dbb61f390d8c8274528f9fdbff983806
  React-RCTVibration: 0e5326220719aca12473d703aa46693e3b4ce67a
  React-rendererconsistency: 68db5a64f0c42b0337e25ba7b0e9513caae1389d
  React-renderercss: eeb482d2790028a9b47ce9c214397ae2f4e2a7c5
  React-rendererdebug: c6e3b7583c2f0802cb3b4cf19f714853fa9ac670
  React-rncore: 0f64cacb1becc6f89c99018ca920d012f9044ebd
  React-RuntimeApple: 1b01b6dcbd092f33cd00f724e696ae6380d99876
  React-RuntimeCore: 7ea609cea4f40fe2eb1cb56c048ca41293d15ce5
  React-runtimeexecutor: d60846710facedd1edb70c08b738119b3ee2c6c2
  React-runtimescheduler: 2e7955d87ed88c1adbf0521ca5de93a9afe6e959
  React-timing: beb0ba912f9ffc1a6758afa767ae5c03302dc9ee
  React-utils: eab34e428c3c3b0b036a819050f12809d025c72a
  ReactAppDependencyProvider: d5dcc564f129632276bd3184e60f053fcd574d6b
  ReactCodegen: 6542da7cd794aa1daf30661dc947b9afea18cae6
  ReactCommon: 8bf1181573180ce4c65f8e51f8d0ddbf6e4ec2aa
  ReactNativeAdsFacebook: 70f6e5be7deb340f0ca1d05a37f9dbac408874dc
  ReactNativeExceptionHandler: b11ff67c78802b2f62eed0e10e75cb1ef7947c60
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  RNAppleAuthentication: b2d8b5ccba86b5b1e55a1a2e858f7ac6d8a21816
  RNBootSplash: a8b695ac52b9285c3210db727d09dabd46d899a1
  RNCAsyncStorage: 22114e9a60f662aee9c59348e1fa8e32468bbd65
  RNCClipboard: 3dbcfebe9f6a635ba1bd16506815db916cd23e33
  RNCPushNotificationIOS: 64218f3c776c03d7408284a819b2abfda1834bc8
  RNDateTimePicker: 77a566164727a69833c30b21662fa5dcb7a360a8
  RNDeviceInfo: 59344c19152c4b2b32283005f9737c5c64b42fba
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFBApp: 2c88b4fc4bcecf25ff9077abde08f4b1cbaff4c6
  RNFBAuth: cc6269f150fd5748f34e8ca415a04ada24985aab
  RNFBFirestore: 3e8ab303b9034854255470aa861814c7d3863a18
  RNFBMessaging: 67ede2b8c07a00902f24dd5819b2c75901a75669
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: 14d7bc43f058b90460715c81323104f71b23d6c4
  RNGoogleSignin: 65d911b62ac48a0a63d9c2a4b77547b1e0e6b8c4
  RNImageCropPicker: 44e2807bc410741f35d4c45b6586aedfe3da39d2
  RNLocalize: 07040b0c4b858960ae2fdcb07e95a9afea3e2b9e
  RNPermissions: bc7ebca5edd86d559440a6da99e879c66d2bc596
  RNReanimated: 1efe6fc53194ffce556ea18d1bc34eda893107e7
  RNScreens: 7db5158da76cd71c8d2bda831e2dbb0f3e72412f
  RNShare: 5cdaa38a63340983d2e1f87326a6c877535474dd
  RNSVG: d69f2ad3e6a4f4a6b47b510c64adbd646038a2cc
  RNVectorIcons: b6486adc5bdfa4ac4e5f415c2ca57eabce9f4c27
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Stripe: cdf416cf2efe286f532a6306de0fcaa0ecc8c71a
  stripe-react-native: 55fbdae947bc2698d55193947fd17872d0eab786
  StripeApplePay: efb62ffc08e6cd4f161d77ddb45de2451075c54e
  StripeCore: 9731f05e327c3dcaf7d7abd116840ceaa9482bbe
  StripeFinancialConnections: 46c0049aaab3a179193502bce4a8096eb7b73f55
  StripePayments: dd1867a620b0b8b5e294e9ff2f1f7b7770765f47
  StripePaymentSheet: d155dfde74e90784d054deffb4f561a1f6dd638f
  StripePaymentsUI: c24f990b03a68a7f6fe704b15dd487e7bb6b603e
  StripeUICore: f2d514e900c37436dc5427fdf2c29d68ab1c2935
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  TweetNacl: 3abf4d1d2082b0114e7a67410e300892448951e6
  Yoga: 50518ade05048235d91a78b803336dbb5b159d5d

PODFILE CHECKSUM: 25b27713036a72281972eb3cebfa290cd4b18d67

COCOAPODS: 1.16.2
