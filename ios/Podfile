
# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported
project 'WineLikes',
'Debug' => :debug,
'Release' => :release

prepare_react_native_project!

# linkage = ENV['USE_FRAMEWORKS']
# if linkage != nil
#   Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
#   use_frameworks! :linkage => linkage.to_sym
# end
use_frameworks! :linkage => :static
$RNFirebaseAsStaticFramework = true

target 'WineLikes' do
  config = use_native_modules!

  # Facebook SDKs
  pod 'FBSDKCoreKit'
  pod 'FBSDKLoginKit'
  pod 'FBSDKShareKit'

  # pod 'GoogleSignIn', '~> 7.1.0'

  # ✅ Firebase pods with modular headers to fix warning
  pod 'Firebase/Core', :modular_headers => true
  pod 'Firebase/Auth', :modular_headers => true
  pod 'Firebase/Firestore', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'FirebaseAuthInterop', :modular_headers => true
  pod 'FirebaseAppCheckInterop', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true
  pod 'FirebaseFirestoreInternal', :modular_headers => true
  pod 'RecaptchaInterop', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true


  # Permissions
  permissions_path = '../node_modules/react-native-permissions/ios'
  pod 'Permission-Camera', :path => "#{permissions_path}/Camera/Permission-Camera.podspec"

  # React Native config
  pod 'react-native-config', :path => '../node_modules/react-native-config'
  pod 'react-native-config/Extension', :path => '../node_modules/react-native-config'

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => false,
  )

  # Icons
  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'

  post_install do |installer|
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
    )

    # Fix build flag warnings
    installer.pods_project.targets.each do |target|
      if target.name == 'BoringSSL-GRPC'
        target.source_build_phase.files.each do |file|
          if file.settings && file.settings['COMPILER_FLAGS']
            flags = file.settings['COMPILER_FLAGS'].split
            flags.reject! { |flag| flag == '-GCC_WARN_INHIBIT_ALL_WARNINGS' }
            file.settings['COMPILER_FLAGS'] = flags.join(' ')
          end
        end
      end
    end
  end
end
