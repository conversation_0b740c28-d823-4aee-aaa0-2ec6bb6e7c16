<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>WineLikes</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>9.0.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1298399751555736</string>
				<string>com.googleusercontent.apps.671879550955-4rvulmne36i9lhpojttoct2pg7he27p3</string>
				<string>com.googleusercontent.apps.671879550955-0s8u8v02ie48fe957srkjuhineardqgq</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.671879550955-4rvulmne36i9lhpojttoct2pg7he27p3</string>
				<string>com.googleusercontent.apps.671879550955-0s8u8v02ie48fe957srkjuhineardqgq</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAppID</key>
	<string>1298399751555736</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>winelikes</string>
	<key>GMSApiKey</key>
	<string>AIzaSyDiglL2YNCyPxDTCfGQa8HMeeGNwGvvH3A</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>instagram</string>
		<string>fb</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>WineLikes app requires camera access to take photo.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>WineLikes app requires location access.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>WineLikes app requires Microphone access.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>WineLikes app requires photo library access.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>WineLikes app requires photo library access.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Lato-Medium.ttf</string>
		<string>Lato-Regular.ttf</string>
		<string>Lato-Semibold.ttf</string>
		<string>ObviouslyDemo-CondMedi.otf</string>
		<string>ObviouslyDemo-CondSemi.otf</string>
		<string>ObviouslyDemo-Regu.otf</string>
		<string>ObviouslyDemo-Semi.otf</string>
		<string>Poppins-Medium.ttf</string>
		<string>Poppins-Regular.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>SFProDisplay-Medium.ttf</string>
		<string>ABeeZee-Regular.ttf</string>
		<string>ObviouslyDemo-Medi.otf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
