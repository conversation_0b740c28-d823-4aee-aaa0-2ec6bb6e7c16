# Using Multiple Environment Files with react-native-config

This project uses `react-native-config` to manage environment variables for different build types (development, production, etc.).

## Environment Files
- `.env.dev` — for development builds
- `.env.prod` — for production builds

Each file should contain the appropriate variables, for example:

```
PROD_BASE_URL='https://your-production-url.com'
DEV_BASE_URL='https://your-dev-url.com'
```

## How to Use

### iOS
To run the app with a specific environment file:
```
ENVFILE=.env.prod npx react-native run-ios
```

### Android
To run the app with a specific environment file:
```
ENVFILE=.env.prod npx react-native run-android
```

Replace `.env.prod` with `.env.dev` for development builds.

## Troubleshooting
- If you change environment files, always rebuild the app.
- Make sure the environment files are in the project root.
- If you see `undefined` for any variable, check the spelling and presence in the correct `.env.*` file.

---
For more, see the [react-native-config documentation](https://github.com/luggit/react-native-config).
