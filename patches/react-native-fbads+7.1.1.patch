diff --git a/node_modules/react-native-fbads/ReactNativeAdsFacebook.podspec b/node_modules/react-native-fbads/ReactNativeAdsFacebook.podspec
index 7ba2cee..0671f0d 100644
--- a/node_modules/react-native-fbads/ReactNativeAdsFacebook.podspec
+++ b/node_modules/react-native-fbads/ReactNativeAdsFacebook.podspec
@@ -12,7 +12,9 @@ Pod::Spec.new do |s|
   s.source        = { :git => 'https://github.com/callstack/react-native-fbads', :tag => "v#{package['version']}" }
   s.platform      = :ios, '7.0'
   s.dependency      'React'
-  s.dependency      'FBAudienceNetwork'
+  s.dependency      'FBAudienceNetwork', '6.11.2'
+  s.dependency      'FBSDKCoreKit_Basics'
+  s.dependency      'FBSDKGamingServicesKit'
 
   s.source_files  = 'ios/**/*.{h,m}'
 end
\ No newline at end of file
diff --git a/node_modules/react-native-fbads/ios/ReactNativeAdsFacebook/EXAdSettingsManager.m b/node_modules/react-native-fbads/ios/ReactNativeAdsFacebook/EXAdSettingsManager.m
index 56bb991..335f874 100644
--- a/node_modules/react-native-fbads/ios/ReactNativeAdsFacebook/EXAdSettingsManager.m
+++ b/node_modules/react-native-fbads/ios/ReactNativeAdsFacebook/EXAdSettingsManager.m
@@ -7,7 +7,7 @@
 #import <FBAudienceNetwork/FBAudienceNetwork.h>
 #import <AppTrackingTransparency/AppTrackingTransparency.h>
 
-#import <FBSDKCoreKit/FBSDKSettings.h>
+// #import <FBSDKCoreKit/FBSDKSettings.h>
 
 @implementation RCTConvert (EXNativeAdView)
 
@@ -126,7 +126,7 @@ - (void)setBridge:(RCTBridge *)bridge
 
 RCT_EXPORT_METHOD(setAdvertiserIDCollectionEnabled:(BOOL)enabled)
 {
-    [FBSDKSettings setAdvertiserIDCollectionEnabled:enabled];
+    // [FBSDKSettings setAdvertiserIDCollectionEnabled:enabled];
 }
 
 
