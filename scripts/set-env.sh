#!/bin/sh
# Usage: ./scripts/set-env.sh [dev|prod|staging]
# This script sets the correct .env file for local builds

ENV=$1
if [ -z "$ENV" ]; then
  echo "Usage: $0 [dev|prod|staging]"
  exit 1
fi

case $ENV in
  dev)
    cp .env.dev .env
    echo ".env set to .env.dev (development)"
    ;;
  prod)
    cp .env.prod .env
    echo ".env set to .env.prod (production)"
    ;;
  staging)
    cp .env.staging .env
    echo ".env set to .env.staging (staging)"
    ;;
  *)
    echo "Unknown environment: $ENV"
    exit 1
    ;;
esac
